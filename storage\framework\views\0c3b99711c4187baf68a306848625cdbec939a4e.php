

<?php $__env->startSection('title', 'Data Regkan'); ?>

<?php $__env->startPush('styles'); ?>
    <style>
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        .table-responsive {
            border-radius: 0.375rem;
            overflow-x: auto;
        }
        .input-group-text {
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        /* Specific styling for Data Regkan tables */
        .card-body .table-responsive {
            min-height: 400px;
        }

        #table-pasien-baru_wrapper,
        #table-notifikasi-kanker_wrapper,
        #table-registrasi-kanker_wrapper,
        #table-bukan-kanker_wrapper {
            padding: 0;
        }

        /* Ensure consistent table styling */
        .table th {
            font-weight: 600;
            font-size: 0.875rem;
            text-align: center;
            vertical-align: middle;
        }

        .table td {
            font-size: 0.875rem;
            vertical-align: middle;
        }

        /* Compact DataTable Pagination Styling */
        .dataTables_wrapper .dataTables_paginate {
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            padding: 0.25rem 0.5rem !important;
            margin: 0 0.1rem !important;
            font-size: 0.875rem !important;
            min-width: auto !important;
            border-radius: 0.25rem !important;
        }

        /* Comprehensive Active/Current page styling - Multiple selectors for maximum compatibility */
        .dataTables_wrapper .dataTables_paginate .paginate_button.current,
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:focus,
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:active,
        .dataTables_wrapper .dataTables_paginate .paginate_button.current.disabled,
        .dataTables_wrapper .dataTables_paginate .paginate_button[aria-current="page"],
        .dataTables_wrapper .dataTables_paginate .paginate_button.active,
        .dataTables_wrapper .dataTables_paginate .paginate_button.selected,
        .dataTables_wrapper .dataTables_paginate span.current,
        .dataTables_wrapper .dataTables_paginate span[aria-current="page"],
        .dataTables_wrapper .dataTables_paginate a.current,
        .dataTables_wrapper .dataTables_paginate a[aria-current="page"],
        .dataTables_wrapper .dataTables_paginate .current,
        .dataTables_wrapper .dataTables_paginate [aria-current="page"] {
            background-color: #405189 !important;
            background: #405189 !important;
            color: #ffffff !important;
            border: 1px solid #405189 !important;
            border-color: #405189 !important;
            font-weight: 700 !important;
            box-shadow: 0 2px 4px rgba(64, 81, 137, 0.3) !important;
            text-shadow: none !important;
            outline: 2px solid rgba(64, 81, 137, 0.3) !important;
            outline-offset: 1px !important;
        }

        /* Hover state for non-current buttons */
        .dataTables_wrapper .dataTables_paginate .paginate_button:hover:not(.current):not([aria-current="page"]):not(.active):not(.selected) {
            background: #f8f9fa !important;
            border-color: #dee2e6 !important;
            color: #405189 !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
        }

        /* Ensure current page styling overrides hover - Multiple selectors */
        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover,
        .dataTables_wrapper .dataTables_paginate .paginate_button[aria-current="page"]:hover,
        .dataTables_wrapper .dataTables_paginate .paginate_button.active:hover,
        .dataTables_wrapper .dataTables_paginate .paginate_button.selected:hover,
        .dataTables_wrapper .dataTables_paginate span.current:hover,
        .dataTables_wrapper .dataTables_paginate a.current:hover {
            background-color: #405189 !important;
            background: #405189 !important;
            color: #ffffff !important;
            border-color: #405189 !important;
            box-shadow: 0 3px 8px rgba(64, 81, 137, 0.4) !important;
            transform: translateY(-1px) !important;
        }

        .dataTables_wrapper .dataTables_info {
            font-size: 0.875rem;
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }

        .dataTables_wrapper .dataTables_length {
            margin-bottom: 0.5rem;
        }

        .dataTables_wrapper .dataTables_length select {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .dataTables_wrapper .dataTables_filter input {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        /* Additional compact pagination styling */
        .compact-pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 0.1rem;
        }

        .compact-pagination .paginate_button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-height: 2rem;
            border: 1px solid #dee2e6;
            background-color: #ffffff;
            color: #495057;
            text-decoration: none;
            transition: all 0.2s ease-in-out;
        }

        /* Fallback current page styling with maximum specificity */
        .dataTables_wrapper .compact-pagination .paginate_button.current,
        .dataTables_wrapper .compact-pagination .paginate_button[aria-current="page"] {
            background-color: #405189 !important;
            color: #ffffff !important;
            border-color: #405189 !important;
            font-weight: 600 !important;
            position: relative;
        }

        /* Add a subtle indicator for current page */
        .dataTables_wrapper .compact-pagination .paginate_button.current::before,
        .dataTables_wrapper .compact-pagination .paginate_button[aria-current="page"]::before,
        .dataTables_wrapper .compact-pagination .paginate_button.force-current-styling::before {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 2px;
            background-color: #ffffff;
            border-radius: 1px;
        }

        /* Force current styling class - highest priority */
        .dataTables_wrapper .dataTables_paginate .paginate_button.force-current-styling,
        .dataTables_wrapper .compact-pagination .paginate_button.force-current-styling {
            background-color: #405189 !important;
            background: #405189 !important;
            color: #ffffff !important;
            border: 1px solid #405189 !important;
            border-color: #405189 !important;
            font-weight: 700 !important;
            box-shadow: 0 2px 4px rgba(64, 81, 137, 0.3) !important;
            outline: 2px solid rgba(64, 81, 137, 0.3) !important;
            outline-offset: 1px !important;
            text-shadow: none !important;
            position: relative !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.force-current-styling:hover,
        .dataTables_wrapper .compact-pagination .paginate_button.force-current-styling:hover {
            background-color: #405189 !important;
            background: #405189 !important;
            color: #ffffff !important;
            border-color: #405189 !important;
            box-shadow: 0 3px 8px rgba(64, 81, 137, 0.4) !important;
            transform: translateY(-1px) !important;
        }

        /* Disabled button styling */
        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
            background: #f8f9fa !important;
            color: #6c757d !important;
            border-color: #dee2e6 !important;
            cursor: not-allowed !important;
            opacity: 0.6 !important;
            box-shadow: none !important;
        }

        /* Compact pagination for smaller screens */
        @media (max-width: 768px) {
            .dataTables_wrapper .dataTables_paginate .paginate_button {
                padding: 0.2rem 0.4rem !important;
                margin: 0 0.05rem !important;
                font-size: 0.8rem !important;
            }

            /* Mobile current page styling */
            .dataTables_wrapper .dataTables_paginate .paginate_button.current,
            .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
                background-color: #405189 !important;
                color: #ffffff !important;
                border-color: #405189 !important;
                font-weight: 600 !important;
                box-shadow: 0 1px 3px rgba(64, 81, 137, 0.3) !important;
            }

            .dataTables_wrapper .dataTables_info,
            .dataTables_wrapper .dataTables_length,
            .dataTables_wrapper .dataTables_filter {
                font-size: 0.8rem;
            }
        }

        /* Mobile responsive adjustments */
        @media (max-width: 768px) {
            .card-body .table-responsive {
                min-height: 300px;
            }

            .table th,
            .table td {
                font-size: 0.8rem;
                padding: 0.5rem 0.25rem;
            }
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">Data Regkan</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Data Regkan</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>


    <div class="row">
        
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Pasien Baru</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-pasien-baru" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Pasien Notifikasi Kanker</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-notifikasi-kanker" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $daftarPasienNotifikasiKanker; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pasien): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($pasien['no_mr']); ?></td>
                                        <td><?php echo e($pasien['nama']); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="2" class="text-center">Tidak ada data</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Pasien Registrasi Kanker</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-registrasi-kanker" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $daftarPasienRegistrasiKanker; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pasien): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($pasien['no_mr']); ?></td>
                                        <td><?php echo e($pasien['nama']); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="2" class="text-center">Tidak ada data</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">Daftar Bukan Registrasi Kanker</h4>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="table-bukan-kanker" class="table table-bordered w-100">
                            <thead>
                                <tr style="background-color: #405189; color: white;">
                                    <th style="width: 30%;">No MR</th>
                                    <th style="width: 70%;">Nama</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $daftarBukanRegistrasiKanker; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pasien): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($pasien['no_mr']); ?></td>
                                        <td><?php echo e($pasien['nama']); ?></td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="2" class="text-center">Tidak ada data</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        $(document).ready(function() {
            // --- Inisialisasi DataTables Server-side untuk Pasien Baru ---
            if ($('#table-pasien-baru').length) {
                $('#table-pasien-baru').DataTable({
                    "destroy": true,
                    "processing": true,
                    "serverSide": true,
                    "ajax": "<?php echo e(route('data-regkan.data')); ?>",
                    "columns": [
                        { "data": "NORM" },
                        { "data": "NAMA_LENGKAP" }
                    ],
                    "dom": '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>><"row"<"col-sm-12"tr>><"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
                    "pagingType": "simple_numbers",
                    "responsive": true,
                    "autoWidth": false,
                    "language": {
                        "lengthMenu": "Tampilkan _MENU_ entri",
                        "zeroRecords": "Tidak ada data yang cocok",
                        "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
                        "infoEmpty": "Tidak ada data tersedia",
                        "infoFiltered": "(difilter dari _MAX_ total entri)",
                        "search": "Cari:",
                        "processing": '<i class="fa fa-spinner fa-spin"></i> Sedang memuat...',
                        "paginate": {
                            "first": "Awal",
                            "last": "Akhir",
                            "next": "Berikutnya",
                            "previous": "Sebelumnya"
                        }
                    }
                });
            }

            // --- Inisialisasi DataTables Client-side untuk tabel lainnya ---
            var clientSideTables = ['#table-notifikasi-kanker', '#table-registrasi-kanker', '#table-bukan-kanker'];
            clientSideTables.forEach(function(tableId) {
                if ($(tableId).length) {
                    $(tableId).DataTable({
                        "dom": '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>><"row"<"col-sm-12"tr>><"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                        "pageLength": 5,
                        "lengthMenu": [[5, 10, 25], [5, 10, 25]],
                        "pagingType": "simple_numbers",
                        "responsive": true,
                        "autoWidth": false,
                        "language": {
                            "lengthMenu": "Tampilkan _MENU_ entri",
                            "zeroRecords": "Tidak ada data",
                            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ entri",
                            "infoEmpty": "Tidak ada data",
                            "search": "Cari:",
                            "paginate": {
                                "next": "Berikutnya",
                                "previous": "Sebelumnya"
                            }
                        }
                    });
                }
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\regkan\resources\views/data-regkan/index.blade.php ENDPATH**/ ?>