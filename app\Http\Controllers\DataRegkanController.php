<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DataRegkanController extends Controller
{
    /**
     * Display the data regkan page
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // Data dummy untuk kategori lainnya (bisa dihapus jika tidak perlu)
        $daftarPasienNotifikasiKanker = [
            ['no_mr' => '101', 'nama' => '<PERSON> Dewi'],
            ['no_mr' => '102', 'nama' => '<PERSON><PERSON> Widodo'],
        ];

        $daftarPasienRegistrasiKanker = [
            ['no_mr' => '201', 'nama' => 'Andi Pratama'],
            ['no_mr' => '202', 'nama' => 'Lestari Wati'],
        ];

        $daftarBukanRegistrasiKanker = [
            ['no_mr' => '301', 'nama' => 'Dewi Sartika'],
            ['no_mr' => '302', 'nama' => 'Bambang Sutrisno'],
        ];

        return view('data-regkan.index', compact(
            'daftarPasienNotifikasiKanker',
            'daftarPasienRegistrasiKanker',
            'daftarBukanRegistrasiKanker'
        ));
    }

    /**
     * Provide data for DataTables.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function data(Request $request)
    {
        $query = "
            SELECT mp.NORM, CONCAT(IF(mp.GELAR_DEPAN='' OR mp.GELAR_DEPAN IS NULL,'',CONCAT(mp.GELAR_DEPAN,'. ')),UPPER(mp.NAMA),IF(mp.GELAR_BELAKANG='' OR mp.GELAR_BELAKANG IS NULL,'',CONCAT(', ',mp.GELAR_BELAKANG))) NAMA_LENGKAP
            FROM master.pasien mp
            WHERE year(mp.TANGGAL) >= '2023'
            AND mp.`STATUS` != 0
        ";

        $totalData = DB::select("SELECT COUNT(*) as count FROM master.pasien mp WHERE year(mp.TANGGAL) >= '2023' AND mp.`STATUS` != 0")[0]->count;
        $totalFiltered = $totalData;

        if ($request->has('search') && $request->input('search')['value'] != '') {
            $search = $request->input('search')['value'];
            $query .= " AND (mp.NORM LIKE '%" . $search . "%' OR mp.NAMA LIKE '%" . $search . "%')";
            
            // This is not perfect, a subquery would be better for filtered count
            $totalFiltered = DB::select("SELECT COUNT(*) as count FROM master.pasien mp WHERE year(mp.TANGGAL) >= '2023' AND mp.`STATUS` != 0 AND (mp.NORM LIKE '%" . $search . "%' OR mp.NAMA LIKE '%" . $search . "%')")[0]->count;
        }

        $columns = ['mp.NORM', 'NAMA_LENGKAP'];
        if ($request->has('order')) {
            $orderColumn = $columns[$request->input('order')[0]['column']];
            $orderDir = $request->input('order')[0]['dir'];
            $query .= " ORDER BY " . $orderColumn . " " . $orderDir;
        } else {
            $query .= " ORDER BY mp.NORM ASC";
        }

        if ($request->has('length') && $request->input('length') != -1) {
            $start = $request->input('start');
            $length = $request->input('length');
            $query .= " LIMIT " . $start . ", " . $length;
        }

        $data = DB::select($query);

        $json_data = [
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($totalData),
            "recordsFiltered" => intval($totalFiltered),
            "data"            => $data
        ];

        return response()->json($json_data);
    }

    /**
     * Test method untuk debugging
     */
    public function test()
    {
        try {
            // Query untuk daftar pasien baru dari database
            $daftarPasienBaru = DB::select("
                SELECT mp.NORM as no_mr,
                       CONCAT(
                           IF(mp.GELAR_DEPAN='' OR mp.GELAR_DEPAN IS NULL,'',CONCAT(mp.GELAR_DEPAN,'. ')),
                           UPPER(mp.NAMA),
                           IF(mp.GELAR_BELAKANG='' OR mp.GELAR_BELAKANG IS NULL,'',CONCAT(', ',mp.GELAR_BELAKANG))
                       ) as nama
                FROM master.pasien mp
                WHERE year(mp.TANGGAL) >= '2023'
                AND mp.STATUS != 0
                ORDER BY mp.NORM ASC
                LIMIT 10
            ");
        } catch (\Exception $e) {
            // Jika query database gagal, gunakan data dummy
            $daftarPasienBaru = [
                ['no_mr' => '001', 'nama' => 'Ahmad Suryadi (DB Error)'],
                ['no_mr' => '002', 'nama' => 'Siti Nurhaliza (DB Error)'],
                ['no_mr' => '003', 'nama' => 'Budi Santoso (DB Error)'],
            ];
        }

        // Data dummy untuk kategori lainnya
        $daftarPasienNotifikasiKanker = [
            ['no_mr' => '101', 'nama' => 'Maria Dewi'],
            ['no_mr' => '102', 'nama' => 'Joko Widodo'],
            ['no_mr' => '103', 'nama' => 'Rina Sari'],
            ['no_mr' => '104', 'nama' => 'Agus Salim'],
        ];

        $daftarPasienRegistrasiKanker = [
            ['no_mr' => '201', 'nama' => 'Andi Pratama'],
            ['no_mr' => '202', 'nama' => 'Lestari Wati'],
            ['no_mr' => '203', 'nama' => 'Hendra Gunawan'],
            ['no_mr' => '204', 'nama' => 'Sari Indah'],
            ['no_mr' => '205', 'nama' => 'Budi Hartono'],
        ];

        $daftarBukanRegistrasiKanker = [
            ['no_mr' => '301', 'nama' => 'Dewi Sartika'],
            ['no_mr' => '302', 'nama' => 'Bambang Sutrisno'],
            ['no_mr' => '303', 'nama' => 'Indira Sari'],
        ];

        return view('data-regkan.test', compact(
            'daftarPasienBaru',
            'daftarPasienNotifikasiKanker',
            'daftarPasienRegistrasiKanker',
            'daftarBukanRegistrasiKanker'
        ));
    }
}
